# Python
__pycache__/
*.py[cod]
*$py.class

# Django-specific
*.log
*.pot
*.pyc
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/

# If using Django migrations
*/migrations/__pycache__/
*/migrations/*.pyc
*/migrations/*.pyo
*/migrations/*.pyd
*/migrations/*.db

# Virtual environments
env/
venv/
ENV/
VENV/
.env
.venv
.Python

# pip tools
pip-log.txt
pip-delete-this-directory.txt

# VS Code
.vscode/

# PyCharm
.idea/

# MacOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Test and coverage
.coverage
htmlcov/
.tox/
.nox/
.cache/
.pytest_cache/

# MyPy and other static checkers
.mypy_cache/
.dmypy.json
.pyre/

# Environments
.env
.env.*

# Sentry CLI
.sentryclirc
